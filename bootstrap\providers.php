<?php

return [
    App\Providers\AppServiceProvider::class,
    App\Providers\Filament\AdminPanelProvider::class,
    Modules\ModuleBuilder\app\Providers\ModuleBuilderServiceProvider::class,
    // Generated module providers will be automatically added here by the module builder
    Mo<PERSON>les\Shop\app\Providers\ShopServiceProvider::class,
    Modules\Contacts\app\Providers\ContactsServiceProvider::class,
    Modules\Deals\app\Providers\DealsServiceProvider::class,
    Modules\Activities\app\Providers\ActivitiesServiceProvider::class,
    Modules\Posts\app\Providers\PostsServiceProvider::class,
    Modules\Categories\app\Providers\CategoriesServiceProvider::class,
    Modules\Comments\app\Providers\CommentsServiceProvider::class,
    Modules\Projects\app\Providers\ProjectsServiceProvider::class,
    Modules\Tasks\app\Providers\TasksServiceProvider::class,
    Modules\Teams\app\Providers\TeamsServiceProvider::class,
    Modules\Products\app\Providers\ProductsServiceProvider::class,
    Modules\Suppliers\app\Providers\SuppliersServiceProvider::class,
    Modules\Warehouses\app\Providers\WarehousesServiceProvider::class,
    Modules\User\app\Providers\UserServiceProvider::class,
    Modules\Category\app\Providers\CategoryServiceProvider::class,
    Modules\Product\app\Providers\ProductServiceProvider::class,
    Modules\Order\app\Providers\OrderServiceProvider::class,
    Modules\Project\app\Providers\ProjectServiceProvider::class,
    Modules\Task\app\Providers\TaskServiceProvider::class,
    Modules\ProjectMember\app\Providers\ProjectMemberServiceProvider::class,
    Modules\Post\app\Providers\PostServiceProvider::class,
    Modules\Comment\app\Providers\CommentServiceProvider::class,
    Modules\Ecommerce\app\Providers\EcommerceServiceProvider::class,
    Modules\BlogPlatform\app\Providers\BlogPlatformServiceProvider::class,
    Modules\LearningManagement\app\Providers\LearningManagementServiceProvider::class,
    Modules\ImportedModule\app\Providers\ImportedModuleServiceProvider::class,
    Modules\CRM\app\Providers\CRMServiceProvider::class,
];
