<?php

namespace Modules\ERDDesigner\app\Services;

use Modules\ERDDesigner\app\Models\ErdProject;
use Modules\ERDDesigner\app\Models\ErdTable;
use Modules\ERDDesigner\app\Models\ErdField;
use Modules\ERDDesigner\app\Models\ErdRelationship;
use Modules\ERDDesigner\app\Models\ErdIndex;

class SqlImportService
{
    private array $parsedTables = [];
    private array $parsedRelationships = [];
    private int $tablePositionX = 100;
    private int $tablePositionY = 100;

    /**
     * Import SQL into ERD project
     */
    public function importToProject(ErdProject $project, string $sql): void
    {
        // Clear existing data
        $project->tables()->delete();
        $project->relationships()->delete();

        // Parse SQL
        $this->parseSql($sql);

        // Create tables and fields
        $this->createTables($project);

        // Create relationships
        $this->createRelationships($project);

        // Update project
        $project->update([
            'updated_by' => auth()->id()
        ]);
    }

    /**
     * Parse SQL string
     */
    private function parseSql(string $sql): void
    {
        // Clean up SQL
        $sql = $this->cleanSql($sql);

        // Parse CREATE TABLE statements
        $this->parseCreateTables($sql);

        // Parse ALTER TABLE statements for foreign keys
        $this->parseAlterTables($sql);
    }

    /**
     * Clean SQL string
     */
    private function cleanSql(string $sql): string
    {
        // Remove comments
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);

        // Remove extra whitespace
        $sql = preg_replace('/\s+/', ' ', $sql);

        return trim($sql);
    }

    /**
     * Parse CREATE TABLE statements
     */
    private function parseCreateTables(string $sql): void
    {
        $pattern = '/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?\s*\((.*?)\)(?:\s*ENGINE\s*=\s*(\w+))?(?:\s*DEFAULT\s+CHARSET\s*=\s*(\w+))?(?:\s*COLLATE\s*=\s*(\w+))?(?:\s*COMMENT\s*=\s*[\'"]([^\'"]*)[\'"])?/is';

        preg_match_all($pattern, $sql, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $tableName = $match[1];
            $tableDefinition = $match[2];
            $engine = $match[3] ?? 'InnoDB';
            $charset = $match[4] ?? 'utf8mb4';
            $collation = $match[5] ?? 'utf8mb4_unicode_ci';
            $comment = $match[6] ?? '';

            $this->parsedTables[$tableName] = [
                'name' => $tableName,
                'engine' => $engine,
                'charset' => $charset,
                'collation' => $collation,
                'comment' => $comment,
                'fields' => $this->parseTableFields($tableDefinition),
                'indexes' => $this->parseTableIndexes($tableDefinition),
                'position_x' => $this->tablePositionX,
                'position_y' => $this->tablePositionY
            ];

            // Update position for next table
            $this->tablePositionX += 250;
            if ($this->tablePositionX > 1000) {
                $this->tablePositionX = 100;
                $this->tablePositionY += 200;
            }
        }
    }

    /**
     * Parse table fields from CREATE TABLE definition
     */
    private function parseTableFields(string $definition): array
    {
        $fields = [];
        $lines = explode(',', $definition);

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip constraints and indexes
            if (preg_match('/^\s*(PRIMARY\s+KEY|KEY|UNIQUE|INDEX|CONSTRAINT|FOREIGN\s+KEY)/i', $line)) {
                continue;
            }

            // Parse field definition
            if (preg_match('/`?(\w+)`?\s+(\w+)(?:\(([^)]+)\))?\s*(.*)/i', $line, $matches)) {
                $fieldName = $matches[1];
                $fieldType = strtolower($matches[2]);
                $fieldParams = $matches[3] ?? '';
                $fieldOptions = $matches[4] ?? '';

                $field = [
                    'name' => $fieldName,
                    'type' => $fieldType,
                    'length' => null,
                    'precision' => null,
                    'scale' => null,
                    'is_nullable' => !preg_match('/NOT\s+NULL/i', $fieldOptions),
                    'is_primary' => false,
                    'is_unique' => false,
                    'is_auto_increment' => preg_match('/AUTO_INCREMENT/i', $fieldOptions),
                    'is_foreign_key' => false,
                    'default_value' => null,
                    'comment' => ''
                ];

                // Parse field parameters
                if ($fieldParams) {
                    if (in_array($fieldType, ['varchar', 'char', 'varbinary', 'binary'])) {
                        $field['length'] = (int)$fieldParams;
                    } elseif ($fieldType === 'decimal') {
                        $params = explode(',', $fieldParams);
                        $field['precision'] = (int)($params[0] ?? 0);
                        $field['scale'] = (int)($params[1] ?? 0);
                    } elseif (in_array($fieldType, ['float', 'double'])) {
                        $params = explode(',', $fieldParams);
                        $field['precision'] = (int)($params[0] ?? 0);
                        if (isset($params[1])) {
                            $field['scale'] = (int)$params[1];
                        }
                    }
                }

                // Parse default value
                if (preg_match('/DEFAULT\s+([^,\s]+)/i', $fieldOptions, $defaultMatch)) {
                    $defaultValue = trim($defaultMatch[1], '\'"');
                    if (strtolower($defaultValue) !== 'null') {
                        $field['default_value'] = $defaultValue;
                    }
                }

                // Parse comment
                if (preg_match('/COMMENT\s+[\'"]([^\'"]*)[\'"]/', $fieldOptions, $commentMatch)) {
                    $field['comment'] = $commentMatch[1];
                }

                $fields[] = $field;
            }
        }

        return $fields;
    }

    /**
     * Parse table indexes from CREATE TABLE definition
     */
    private function parseTableIndexes(string $definition): array
    {
        $indexes = [];
        $lines = explode(',', $definition);

        foreach ($lines as $line) {
            $line = trim($line);

            // Parse PRIMARY KEY
            if (preg_match('/PRIMARY\s+KEY\s*\(\s*`?([^)]+)`?\s*\)/i', $line, $matches)) {
                $fields = array_map('trim', explode(',', str_replace('`', '', $matches[1])));
                $indexes[] = [
                    'name' => 'PRIMARY',
                    'type' => 'PRIMARY',
                    'fields' => $fields,
                    'is_unique' => true,
                    'is_primary' => true
                ];

                // Mark fields as primary
                foreach ($fields as $fieldName) {
                    $this->markFieldAsPrimary($fieldName);
                }
            }

            // Parse UNIQUE KEY
            elseif (preg_match('/UNIQUE\s+KEY\s+`?(\w+)`?\s*\(\s*`?([^)]+)`?\s*\)/i', $line, $matches)) {
                $indexName = $matches[1];
                $fields = array_map('trim', explode(',', str_replace('`', '', $matches[2])));
                $indexes[] = [
                    'name' => $indexName,
                    'type' => 'UNIQUE',
                    'fields' => $fields,
                    'is_unique' => true,
                    'is_primary' => false
                ];
            }

            // Parse regular KEY/INDEX
            elseif (preg_match('/(?:KEY|INDEX)\s+`?(\w+)`?\s*\(\s*`?([^)]+)`?\s*\)/i', $line, $matches)) {
                $indexName = $matches[1];
                $fields = array_map('trim', explode(',', str_replace('`', '', $matches[2])));
                $indexes[] = [
                    'name' => $indexName,
                    'type' => 'INDEX',
                    'fields' => $fields,
                    'is_unique' => false,
                    'is_primary' => false
                ];
            }
        }

        return $indexes;
    }

    /**
     * Mark field as primary key
     */
    private function markFieldAsPrimary(string $fieldName): void
    {
        foreach ($this->parsedTables as &$table) {
            foreach ($table['fields'] as &$field) {
                if ($field['name'] === $fieldName) {
                    $field['is_primary'] = true;
                    break 2;
                }
            }
        }
    }

    /**
     * Parse ALTER TABLE statements for foreign keys
     */
    private function parseAlterTables(string $sql): void
    {
        $pattern = '/ALTER\s+TABLE\s+`?(\w+)`?\s+ADD\s+(?:CONSTRAINT\s+`?(\w+)`?\s+)?FOREIGN\s+KEY\s*\(\s*`?(\w+)`?\s*\)\s+REFERENCES\s+`?(\w+)`?\s*\(\s*`?(\w+)`?\s*\)(?:\s+ON\s+UPDATE\s+(\w+))?(?:\s+ON\s+DELETE\s+(\w+))?/i';

        preg_match_all($pattern, $sql, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $sourceTable = $match[1];
            $constraintName = $match[2] ?? "fk_{$sourceTable}_{$match[3]}";
            $sourceField = $match[3];
            $targetTable = $match[4];
            $targetField = $match[5];
            $onUpdate = $match[6] ?? 'RESTRICT';
            $onDelete = $match[7] ?? 'RESTRICT';

            $this->parsedRelationships[] = [
                'name' => $constraintName,
                'type' => 'many_to_one',
                'source_table' => $sourceTable,
                'target_table' => $targetTable,
                'source_field' => $sourceField,
                'target_field' => $targetField,
                'on_update' => strtoupper($onUpdate),
                'on_delete' => strtoupper($onDelete)
            ];

            // Mark source field as foreign key
            $this->markFieldAsForeignKey($sourceTable, $sourceField);
        }
    }

    /**
     * Mark field as foreign key
     */
    private function markFieldAsForeignKey(string $tableName, string $fieldName): void
    {
        if (isset($this->parsedTables[$tableName])) {
            foreach ($this->parsedTables[$tableName]['fields'] as &$field) {
                if ($field['name'] === $fieldName) {
                    $field['is_foreign_key'] = true;
                    break;
                }
            }
        }
    }

    /**
     * Create tables in the project
     */
    private function createTables(ErdProject $project): void
    {
        foreach ($this->parsedTables as $tableData) {
            $table = ErdTable::create([
                'project_id' => $project->id,
                'name' => $tableData['name'],
                'display_name' => ucfirst($tableData['name']),
                'description' => $tableData['comment'],
                'position_x' => $tableData['position_x'],
                'position_y' => $tableData['position_y'],
                'width' => 200,
                'height' => 150,
                'color' => '#ffffff',
                'engine' => $tableData['engine'],
                'charset' => $tableData['charset'],
                'collation' => $tableData['collation'],
                'comment' => $tableData['comment']
            ]);

            // Create fields
            foreach ($tableData['fields'] as $index => $fieldData) {
                ErdField::create(array_merge($fieldData, [
                    'table_id' => $table->id,
                    'display_name' => ucfirst($fieldData['name']),
                    'order' => $index,
                    'form_type' => $this->getFormType($fieldData['type']),
                    'validation_rules' => $this->getValidationRules($fieldData)
                ]));
            }

            // Create indexes
            foreach ($tableData['indexes'] as $indexData) {
                ErdIndex::create(array_merge($indexData, [
                    'table_id' => $table->id
                ]));
            }
        }
    }

    /**
     * Create relationships in the project
     */
    private function createRelationships(ErdProject $project): void
    {
        foreach ($this->parsedRelationships as $relData) {
            $sourceTable = ErdTable::where('project_id', $project->id)
                ->where('name', $relData['source_table'])
                ->first();

            $targetTable = ErdTable::where('project_id', $project->id)
                ->where('name', $relData['target_table'])
                ->first();

            if ($sourceTable && $targetTable) {
                $sourceField = ErdField::where('table_id', $sourceTable->id)
                    ->where('name', $relData['source_field'])
                    ->first();

                $targetField = ErdField::where('table_id', $targetTable->id)
                    ->where('name', $relData['target_field'])
                    ->first();

                if ($sourceField && $targetField) {
                    ErdRelationship::create([
                        'project_id' => $project->id,
                        'name' => $relData['name'],
                        'type' => $relData['type'],
                        'source_table_id' => $sourceTable->id,
                        'target_table_id' => $targetTable->id,
                        'source_field_id' => $sourceField->id,
                        'target_field_id' => $targetField->id,
                        'on_update' => $relData['on_update'],
                        'on_delete' => $relData['on_delete'],
                        'cardinality_source' => 'zero_or_many',
                        'cardinality_target' => 'exactly_one'
                    ]);
                }
            }
        }
    }

    /**
     * Get appropriate form type for field type
     */
    private function getFormType(string $fieldType): string
    {
        $typeMap = [
            'varchar' => 'text',
            'char' => 'text',
            'text' => 'textarea',
            'mediumtext' => 'textarea',
            'longtext' => 'textarea',
            'int' => 'number',
            'bigint' => 'number',
            'tinyint' => 'number',
            'smallint' => 'number',
            'mediumint' => 'number',
            'decimal' => 'number',
            'float' => 'number',
            'double' => 'number',
            'date' => 'date',
            'datetime' => 'datetime',
            'timestamp' => 'datetime',
            'time' => 'time',
            'year' => 'number',
            'enum' => 'select',
            'set' => 'select',
            'json' => 'json',
            'boolean' => 'toggle'
        ];

        return $typeMap[$fieldType] ?? 'text';
    }

    /**
     * Get validation rules for field
     */
    private function getValidationRules(array $fieldData): string
    {
        $rules = [];

        if (!$fieldData['is_nullable']) {
            $rules[] = 'required';
        }

        if ($fieldData['length'] && in_array($fieldData['type'], ['varchar', 'char'])) {
            $rules[] = "max:{$fieldData['length']}";
        }

        if (in_array($fieldData['type'], ['int', 'bigint', 'tinyint', 'smallint', 'mediumint'])) {
            $rules[] = 'integer';
        }

        if (in_array($fieldData['type'], ['decimal', 'float', 'double'])) {
            $rules[] = 'numeric';
        }

        if ($fieldData['type'] === 'email') {
            $rules[] = 'email';
        }

        if ($fieldData['type'] === 'url') {
            $rules[] = 'url';
        }

        return implode('|', $rules);
    }
}
