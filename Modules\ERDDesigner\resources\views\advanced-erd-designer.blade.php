<x-filament-panels::page>
    <div class="erd-designer-container">
        <!-- Project Header -->
        <div class="bg-white border-b border-gray-200 p-4 mb-4 rounded-lg shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">{{ $project->name ?? 'ERD Project' }}</h2>
                    <p class="text-gray-600 text-sm">{{ $project->description ?? 'Database design project' }}</p>
                </div>

                <div class="flex items-center space-x-2">
                    <button wire:click="addTable"
                            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Add Table
                    </button>

                    <button wire:click="importSQL"
                            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m3-3V10"></path>
                        </svg>
                        Import SQL
                    </button>

                    <button wire:click="exportSQL"
                            class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        Export SQL
                    </button>

                    <button wire:click="exportToModuleBuilder"
                            class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3"></path>
                        </svg>
                        To Module Builder
                    </button>
                </div>
            </div>
        </div>

        <!-- Canvas Container -->
        <div class="relative bg-gray-50 border border-gray-200 rounded-lg overflow-hidden" style="height: 600px;">
            <!-- Grid Background -->
            <div x-show="showGrid" class="absolute inset-0 opacity-20" 
                 :style="`background-image: repeating-linear-gradient(0deg, #ccc, #ccc 1px, transparent 1px, transparent ${gridSize * zoom}px), repeating-linear-gradient(90deg, #ccc, #ccc 1px, transparent 1px, transparent ${gridSize * zoom}px);`">
            </div>
            
            <!-- Canvas -->
            <div class="absolute inset-0 cursor-move" 
                 @mousedown="startPan($event)"
                 @mousemove="pan($event)"
                 @mouseup="endPan()"
                 @wheel="handleWheel($event)">
                
                <!-- Canvas Content -->
                <div class="relative" 
                     :style="`transform: translate(${panX}px, ${panY}px) scale(${zoom}); transform-origin: 0 0;`">
                    
                    <!-- Tables -->
                    <template x-for="table in tables" :key="table.id">
                        <div class="absolute bg-white border border-gray-300 rounded-lg shadow-lg cursor-pointer"
                             :style="`left: ${table.position_x}px; top: ${table.position_y}px; width: ${table.width}px; min-height: ${table.height}px; background-color: ${table.color};`"
                             @mousedown="startDrag($event, table)"
                             @click="selectTable(table)">
                            
                            <!-- Table Header -->
                            <div class="bg-gray-800 text-white px-3 py-2 rounded-t-lg">
                                <h3 class="font-semibold text-sm" x-text="table.display_name || table.name"></h3>
                            </div>
                            
                            <!-- Table Fields -->
                            <div class="p-2">
                                <template x-for="field in table.fields" :key="field.id">
                                    <div class="flex items-center justify-between py-1 px-2 text-xs hover:bg-gray-100 rounded"
                                         :class="field.is_primary ? 'font-bold text-yellow-700' : field.is_foreign_key ? 'text-blue-700' : 'text-gray-700'">
                                        <div class="flex items-center space-x-2">
                                            <!-- Field Icon -->
                                            <span class="w-3 h-3 flex items-center justify-center">
                                                <template x-if="field.is_primary">
                                                    <svg class="w-3 h-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 2L3 7v11h4v-6h6v6h4V7l-7-5z"/>
                                                    </svg>
                                                </template>
                                                <template x-if="field.is_foreign_key && !field.is_primary">
                                                    <svg class="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"/>
                                                    </svg>
                                                </template>
                                                <template x-if="!field.is_primary && !field.is_foreign_key">
                                                    <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                </template>
                                            </span>
                                            <span x-text="field.name"></span>
                                        </div>
                                        <span class="text-gray-500 text-xs" x-text="field.type + (field.length ? '(' + field.length + ')' : '')"></span>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                    
                    <!-- Relationships -->
                    <svg class="absolute inset-0 pointer-events-none" style="width: 100%; height: 100%;">
                        <template x-for="relationship in relationships" :key="relationship.id">
                            <g>
                                <!-- Relationship Line -->
                                <line :x1="getRelationshipStart(relationship).x" 
                                      :y1="getRelationshipStart(relationship).y"
                                      :x2="getRelationshipEnd(relationship).x" 
                                      :y2="getRelationshipEnd(relationship).y"
                                      stroke="#4F46E5" 
                                      stroke-width="2" 
                                      marker-end="url(#arrowhead)"/>
                                
                                <!-- Relationship Label -->
                                <text :x="(getRelationshipStart(relationship).x + getRelationshipEnd(relationship).x) / 2"
                                      :y="(getRelationshipStart(relationship).y + getRelationshipEnd(relationship).y) / 2 - 5"
                                      fill="#4F46E5" 
                                      font-size="10" 
                                      text-anchor="middle"
                                      x-text="relationship.type">
                                </text>
                            </g>
                        </template>
                        
                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                    refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#4F46E5"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div x-show="selectedTable" class="mt-4 bg-white border border-gray-200 rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-4">Table Properties</h3>
            <template x-if="selectedTable">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Table Name</label>
                        <input type="text" x-model="selectedTable.name" 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                        <input type="text" x-model="selectedTable.display_name" 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm">
                    </div>
                    <div class="col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea x-model="selectedTable.description" 
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm" rows="2"></textarea>
                    </div>
                </div>
            </template>
        </div>

        <!-- JavaScript -->
        <script>
        function erdDesigner() {
            return {
                // Canvas state
                zoom: 1.0,
                panX: 0,
                panY: 0,
                showGrid: true,
                gridSize: 20,
                
                // Interaction state
                isPanning: false,
                isDragging: false,
                dragStartX: 0,
                dragStartY: 0,
                dragTable: null,
                selectedTable: null,
                
                // Data
                tables: @js($tables ?? []),
                relationships: @js($relationships ?? []),
                
                // Modals
                showImportModal: false,
                
                init() {
                    // Initialize canvas
                    this.loadData();
                },
                
                loadData() {
                    // Load tables and relationships from Livewire component
                    // This would be connected to the backend
                },
                
                // Zoom functions
                zoomIn() {
                    this.zoom = Math.min(this.zoom * 1.2, 3.0);
                },
                
                zoomOut() {
                    this.zoom = Math.max(this.zoom / 1.2, 0.1);
                },
                
                resetZoom() {
                    this.zoom = 1.0;
                    this.panX = 0;
                    this.panY = 0;
                },
                
                handleWheel(event) {
                    event.preventDefault();
                    if (event.deltaY < 0) {
                        this.zoomIn();
                    } else {
                        this.zoomOut();
                    }
                },
                
                // Pan functions
                startPan(event) {
                    if (event.target === event.currentTarget) {
                        this.isPanning = true;
                        this.dragStartX = event.clientX - this.panX;
                        this.dragStartY = event.clientY - this.panY;
                    }
                },
                
                pan(event) {
                    if (this.isPanning) {
                        this.panX = event.clientX - this.dragStartX;
                        this.panY = event.clientY - this.dragStartY;
                    } else if (this.isDragging && this.dragTable) {
                        const rect = event.currentTarget.getBoundingClientRect();
                        const x = (event.clientX - rect.left - this.panX) / this.zoom;
                        const y = (event.clientY - rect.top - this.panY) / this.zoom;
                        
                        this.dragTable.position_x = x - this.dragStartX;
                        this.dragTable.position_y = y - this.dragStartY;
                    }
                },
                
                endPan() {
                    this.isPanning = false;
                    this.isDragging = false;
                    this.dragTable = null;
                },
                
                // Table functions
                startDrag(event, table) {
                    event.stopPropagation();
                    this.isDragging = true;
                    this.dragTable = table;
                    
                    const rect = event.currentTarget.getBoundingClientRect();
                    this.dragStartX = (event.clientX - rect.left) / this.zoom;
                    this.dragStartY = (event.clientY - rect.top) / this.zoom;
                },
                
                selectTable(table) {
                    this.selectedTable = table;
                },
                
                addTable() {
                    // This would call a Livewire method
                    $wire.addTable();
                },
                
                // Relationship functions
                getRelationshipStart(relationship) {
                    const sourceTable = this.tables.find(t => t.id === relationship.source_table_id);
                    if (!sourceTable) return { x: 0, y: 0 };
                    
                    return {
                        x: sourceTable.position_x + sourceTable.width,
                        y: sourceTable.position_y + sourceTable.height / 2
                    };
                },
                
                getRelationshipEnd(relationship) {
                    const targetTable = this.tables.find(t => t.id === relationship.target_table_id);
                    if (!targetTable) return { x: 0, y: 0 };
                    
                    return {
                        x: targetTable.position_x,
                        y: targetTable.position_y + targetTable.height / 2
                    };
                },
                
                // Export functions
                exportSQL() {
                    $wire.exportSQL();
                }
            }
        }
    </script>

        <!-- Styles -->
        <style>
            .erd-designer-container {
                user-select: none;
            }

            .erd-designer-container * {
                box-sizing: border-box;
            }
        </style>
    </div>
</x-filament-panels::page>
