<x-filament-panels::page>
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Database ERD Designer</h2>
        <p class="text-gray-600 mb-6">Visual database design tool with drag & drop interface.</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-semibold text-blue-900 mb-2">🎨 Visual Design</h3>
                <p class="text-blue-700 text-sm">Create database schemas with drag & drop tables and visual relationships.</p>
            </div>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 class="font-semibold text-green-900 mb-2">📥 SQL Import</h3>
                <p class="text-green-700 text-sm">Import existing database schemas from SQL files.</p>
            </div>
            
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h3 class="font-semibold text-purple-900 mb-2">🔗 Module Integration</h3>
                <p class="text-purple-700 text-sm">Seamlessly integrate with Module Builder for code generation.</p>
            </div>
        </div>
        
        <div class="mt-8 flex space-x-4">
            <button wire:click="createNewProject"
                    class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                    wire:loading.attr="disabled">
                <span wire:loading.remove>Create New Project</span>
                <span wire:loading>Creating...</span>
            </button>
            <button wire:click="importSQL"
                    class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                Import SQL
            </button>
            <button wire:click="loadDemo"
                    class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                    wire:loading.attr="disabled">
                <span wire:loading.remove>Load Demo</span>
                <span wire:loading>Loading...</span>
            </button>
        </div>

        @if(count($projects) > 0)
        <div class="mt-8 bg-white border border-gray-200 rounded-lg p-4">
            <h4 class="font-semibold text-gray-900 mb-4">Your ERD Projects</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($projects as $project)
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <h5 class="font-medium text-gray-900 mb-2">{{ $project['name'] }}</h5>
                    <p class="text-gray-600 text-sm mb-3">{{ $project['description'] }}</p>
                    <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                        <span>{{ $project['database_type'] ?? 'mysql' }} (ID: {{ $project['id'] }})</span>
                        <span>{{ \Carbon\Carbon::parse($project['updated_at'])->diffForHumans() }}</span>
                    </div>
                    <a href="/admin/advanced-erd-designer?project={{ $project['id'] }}"
                       class="block w-full bg-blue-100 text-blue-700 px-3 py-2 rounded text-sm hover:bg-blue-200 transition-colors text-center">
                        Open Project
                    </a>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <div class="mt-8 bg-gray-50 rounded-lg p-4">
            <h4 class="font-semibold text-gray-900 mb-2">Features Available:</h4>
            <ul class="text-gray-600 text-sm space-y-1">
                <li>• ✅ Project creation and management</li>
                <li>• ✅ Demo data loading</li>
                <li>• ✅ Permission-based access control</li>
                <li>• ✅ Module Builder integration</li>
                <li>• 🔄 Advanced canvas with zoom & pan (Coming Soon)</li>
                <li>• 🔄 Real-time relationship visualization (Coming Soon)</li>
                <li>• 🔄 SQL import/export functionality (Coming Soon)</li>
            </ul>
        </div>
    </div>
</x-filament-panels::page>
