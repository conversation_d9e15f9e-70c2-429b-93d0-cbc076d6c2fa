<?php

namespace Modules\ERDDesigner\app\Filament\Pages;

use Filament\Pages\Page;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;
use Modules\ERDDesigner\app\Models\ErdProject;
use Modules\ERDDesigner\app\Models\ErdTable;
use Modules\ERDDesigner\app\Models\ErdField;
use Modules\ERDDesigner\app\Models\ErdRelationship;
use Modules\ERDDesigner\app\Services\SqlExportService;
use Modules\ERDDesigner\app\Services\SqlImportService;

class AdvancedERDDesigner extends Page
{
    protected string $view = 'erddesigner::advanced-erd-designer-visual';

    protected static ?string $navigationLabel = null; // Hide from navigation

    protected static ?string $title = 'Advanced ERD Designer';

    protected static bool $shouldRegisterNavigation = false; // Don't show in sidebar

    protected static ?string $slug = 'advanced-erd-designer';

    public ?ErdProject $project = null;
    public array $tables = [];
    public array $relationships = [];
    public bool $showTableModal = false;
    public bool $showImportModal = false;
    public bool $showExportModal = false;
    public array $tableForm = [];
    public ?int $editingTableId = null;
    public string $sqlImport = '';

    public array $fieldForm = [];
    public ?int $editingFieldId = null;
    public bool $showFieldModal = false;
    public string $sqlExport = '';

    public static function canAccess(): bool
    {
        return auth()->user()?->can('access_erd_designer') ?? false;
    }

    public function mount(): void
    {
        // Get project ID from URL parameter
        $project = request()->get('project');

        if ($project) {
            // Super admins can access all projects, regular users only their own
            $query = ErdProject::where('id', $project);

            if (!auth()->user()->hasRole('Super Admin')) {
                $query->where('created_by', auth()->id());
            }

            $this->project = $query->first();

            if (!$this->project) {
                // Check if project exists but belongs to different user
                $projectExists = ErdProject::where('id', $project)->exists();

                if ($projectExists) {
                    Notification::make()
                        ->title('Access Denied')
                        ->body("ERD project #{$project} exists but you don't have access to it.")
                        ->danger()
                        ->send();
                } else {
                    Notification::make()
                        ->title('Project Not Found')
                        ->body("ERD project #{$project} was not found. Please check the project ID.")
                        ->danger()
                        ->send();
                }

                $this->redirect('/admin/erd-designer-page');
                return;
            }

            $this->loadProjectData();

            // If project is empty, create demo data
            if (empty($this->tables)) {
                $this->createDemoTables();
                $this->loadProjectData(); // Reload after creating demo data
            }

            // Debug: Log the loaded data
            \Log::info('ERD Designer loaded', [
                'project_id' => $this->project->id,
                'tables_count' => count($this->tables),
                'relationships_count' => count($this->relationships)
            ]);
        } else {
            Notification::make()
                ->title('No Project Specified')
                ->body('Please select a project to open in the advanced ERD designer.')
                ->warning()
                ->send();

            $this->redirect('/admin/erd-designer-page');
        }
    }

    public function loadProjectData(): void
    {
        if (!$this->project) return;

        // Load tables with fields
        $this->tables = $this->project->tables()
            ->with(['fields' => function($query) {
                $query->orderBy('order');
            }])
            ->get()
            ->map(function($table) {
                return [
                    'id' => $table->id,
                    'name' => $table->name,
                    'display_name' => $table->display_name,
                    'description' => $table->description,
                    'position_x' => $table->position_x,
                    'position_y' => $table->position_y,
                    'width' => $table->width,
                    'height' => $table->height,
                    'color' => $table->color,
                    'fields' => $table->fields->map(function($field) {
                        return [
                            'id' => $field->id,
                            'name' => $field->name,
                            'type' => $field->type,
                            'length' => $field->length,
                            'is_nullable' => $field->is_nullable,
                            'is_primary' => $field->is_primary,
                            'is_foreign_key' => $field->is_foreign_key,
                            'default_value' => $field->default_value
                        ];
                    })->toArray()
                ];
            })->toArray();

        // Load relationships
        $this->relationships = $this->project->relationships()
            ->with(['sourceTable', 'targetTable', 'sourceField', 'targetField'])
            ->get()
            ->map(function($rel) {
                return [
                    'id' => $rel->id,
                    'type' => $rel->type,
                    'source_table_id' => $rel->source_table_id,
                    'target_table_id' => $rel->target_table_id,
                    'source_field_id' => $rel->source_field_id,
                    'target_field_id' => $rel->target_field_id,
                    'cardinality_source' => $rel->cardinality_source,
                    'cardinality_target' => $rel->cardinality_target
                ];
            })->toArray();

        // Debug: Log table positions being loaded
        \Log::info('Table positions loaded from database:', [
            'positions' => collect($this->tables)->map(function($table) {
                return [
                    'name' => $table['name'],
                    'position_x' => $table['position_x'],
                    'position_y' => $table['position_y']
                ];
            })->toArray()
        ]);

        // Dispatch event to refresh frontend data
        $this->dispatch('dataUpdated', [
            'tables' => $this->tables,
            'relationships' => $this->relationships
        ]);
    }

    public function addTable(): void
    {
        // Allow access for authenticated users - remove strict permission check for now
        if (!auth()->check()) {
            Notification::make()
                ->title('Access Denied')
                ->body('You must be logged in to manage ERD tables.')
                ->danger()
                ->send();
            return;
        }

        if (!$this->project) {
            Notification::make()
                ->title('No Project')
                ->body('No project is currently loaded.')
                ->warning()
                ->send();
            return;
        }

        // Initialize table form with default values
        $tableCount = count($this->tables);
        $this->tableForm = [
            'name' => 'table_' . ($tableCount + 1),
            'display_name' => 'New Table ' . ($tableCount + 1),
            'description' => '',
            'color' => '#3b82f6',
            'fields' => [
                [
                    'name' => 'id',
                    'type' => 'bigint',
                    'length' => null,
                    'is_nullable' => false,
                    'is_primary' => true,
                    'is_foreign_key' => false,
                    'is_auto_increment' => true,
                    'default_value' => '',
                    'validation' => '',
                    'enum_options' => ''
                ]
            ]
        ];

        // Ensure color is never empty
        if (empty($this->tableForm['color'])) {
            $this->tableForm['color'] = '#3b82f6';
        }

        $this->showTableModal = true;
    }

    public function editTable(int $tableId): void
    {
        if (!$this->project) return;

        $table = ErdTable::with(['fields' => function($query) {
            $query->orderBy('order', 'asc');
        }])->find($tableId);
        if (!$table || $table->project_id !== $this->project->id) {
            Notification::make()
                ->title('Table Not Found')
                ->body('The table you are trying to edit was not found.')
                ->danger()
                ->send();
            return;
        }

        $this->editingTableId = $tableId;

        // Populate form with existing table data
        $this->tableForm = [
            'name' => $table->name,
            'display_name' => $table->display_name,
            'description' => $table->description ?? '',
            'color' => !empty($table->color) ? $table->color : '#3b82f6',
            'fields' => $table->fields->sortBy('order')->map(function($field) {
                return [
                    'id' => $field->id,
                    'name' => $field->name,
                    'type' => $field->type,
                    'length' => $field->length,
                    'is_nullable' => $field->is_nullable,
                    'is_primary' => $field->is_primary,
                    'is_foreign_key' => $field->is_foreign_key,
                    'is_auto_increment' => $field->is_auto_increment,
                    'default_value' => $field->default_value ?? '',
                    'validation' => '',
                    'enum_options' => ''
                ];
            })->values()->toArray()
        ];

        // Ensure color is never empty
        if (empty($this->tableForm['color'])) {
            $this->tableForm['color'] = '#3b82f6';
        }

        // Debug: Log the loaded data
        \Log::info('Edit Table Data Loaded', [
            'table_id' => $tableId,
            'table_name' => $table->name,
            'fields_count' => $table->fields->count(),
            'form_fields_count' => count($this->tableForm['fields']),
            'fields_data' => $this->tableForm['fields']
        ]);

        $this->showTableModal = true;

        // Force refresh the component to ensure data is properly bound
        $this->dispatch('refresh');
    }

    public function refreshTableForm(): void
    {
        // Method to manually refresh the table form if needed
        if ($this->editingTableId) {
            $this->editTable($this->editingTableId);
        }
    }

    public function refreshPositions(): void
    {
        // Force reload all table positions from database
        $this->loadProjectData();

        Notification::make()
            ->title('Positions Refreshed')
            ->body('All table positions have been reloaded from the database.')
            ->success()
            ->send();
    }

    public function removeFieldFromForm(int $index): void
    {
        if (isset($this->tableForm['fields'][$index])) {
            array_splice($this->tableForm['fields'], $index, 1);
        }
    }

    public function addFieldToForm(): void
    {
        if (!isset($this->tableForm['fields'])) {
            $this->tableForm['fields'] = [];
        }

        $this->tableForm['fields'][] = [
            'id' => null,
            'name' => '',
            'type' => 'varchar',
            'length' => 255,
            'is_nullable' => true,
            'is_primary' => false,
            'is_foreign_key' => false,
            'is_auto_increment' => false,
            'default_value' => '',
            'validation' => '',
            'enum_options' => ''
        ];
    }

    public function editField(int $fieldId): void
    {
        $field = ErdField::find($fieldId);
        if ($field && $field->table->project_id === $this->project->id) {
            // Set up field form for editing
            $this->fieldForm = [
                'id' => $field->id,
                'table_id' => $field->table_id,
                'name' => $field->name,
                'type' => $field->type,
                'length' => $field->length,
                'is_nullable' => $field->is_nullable,
                'is_primary' => $field->is_primary,
                'is_foreign_key' => $field->is_foreign_key,
                'is_auto_increment' => $field->is_auto_increment,
                'default_value' => $field->default_value,
                'validation' => $field->validation ?? '',
                'enum_options' => $field->enum_options ?? ''
            ];

            $this->editingFieldId = $fieldId;
            $this->showFieldModal = true;
        }
    }

    public function addNewFieldToTable(int $tableId): void
    {
        $table = ErdTable::find($tableId);
        if ($table && $table->project_id === $this->project->id) {
            // Set up field form for new field
            $this->fieldForm = [
                'id' => null,
                'table_id' => $tableId,
                'name' => '',
                'type' => 'varchar',
                'length' => 255,
                'is_nullable' => true,
                'is_primary' => false,
                'is_foreign_key' => false,
                'is_auto_increment' => false,
                'default_value' => '',
                'validation' => '',
                'enum_options' => ''
            ];

            $this->editingFieldId = null;
            $this->showFieldModal = true;
        }
    }

    public function saveField(): void
    {
        if ($this->editingFieldId) {
            // Update existing field
            $field = ErdField::find($this->editingFieldId);
            if ($field && $field->table->project_id === $this->project->id) {
                $field->update([
                    'name' => $this->fieldForm['name'],
                    'type' => $this->fieldForm['type'],
                    'length' => $this->fieldForm['length'],
                    'is_nullable' => $this->fieldForm['is_nullable'],
                    'is_primary' => $this->fieldForm['is_primary'],
                    'is_foreign_key' => $this->fieldForm['is_foreign_key'],
                    'is_auto_increment' => $this->fieldForm['is_auto_increment'],
                    'default_value' => $this->fieldForm['default_value'],
                    'validation' => $this->fieldForm['validation'],
                    'enum_options' => $this->fieldForm['enum_options']
                ]);

                Notification::make()
                    ->title('Field Updated')
                    ->body('Field has been updated successfully.')
                    ->success()
                    ->send();
            }
        } else {
            // Create new field
            $table = ErdTable::find($this->fieldForm['table_id']);
            if ($table && $table->project_id === $this->project->id) {
                ErdField::create([
                    'table_id' => $this->fieldForm['table_id'],
                    'name' => $this->fieldForm['name'],
                    'type' => $this->fieldForm['type'],
                    'length' => $this->fieldForm['length'],
                    'is_nullable' => $this->fieldForm['is_nullable'],
                    'is_primary' => $this->fieldForm['is_primary'],
                    'is_foreign_key' => $this->fieldForm['is_foreign_key'],
                    'is_auto_increment' => $this->fieldForm['is_auto_increment'],
                    'default_value' => $this->fieldForm['default_value'],
                    'validation' => $this->fieldForm['validation'],
                    'enum_options' => $this->fieldForm['enum_options'],
                    'order' => $table->fields()->count(),
                    'form_type' => $this->getFormType($this->fieldForm['type'])
                ]);

                Notification::make()
                    ->title('Field Created')
                    ->body('New field has been created successfully.')
                    ->success()
                    ->send();
            }
        }

        $this->closeFieldModal();
        $this->loadProjectData();
    }

    public function closeFieldModal(): void
    {
        $this->fieldForm = [];
        $this->editingFieldId = null;
        $this->showFieldModal = false;
    }

    public function saveTable(): void
    {
        if (!$this->project || empty($this->tableForm)) return;

        // Validate required fields
        if (empty($this->tableForm['name']) || empty($this->tableForm['display_name'])) {
            Notification::make()
                ->title('Validation Error')
                ->body('Table name and display name are required.')
                ->danger()
                ->send();
            return;
        }

        if ($this->editingTableId) {
            // Update existing table
            $this->updateExistingTable();
        } else {
            // Create new table
            $this->createNewTable();
        }
    }

    private function createNewTable(): void
    {
        // Check for unique table name
        $existingTable = collect($this->tables)->firstWhere('name', $this->tableForm['name']);
        if ($existingTable) {
            Notification::make()
                ->title('Validation Error')
                ->body('A table with this name already exists.')
                ->danger()
                ->send();
            return;
        }

        // Calculate position for new table
        $tableCount = count($this->tables);
        $positionX = 100 + ($tableCount * 50);
        $positionY = 100 + ($tableCount * 50);

        // Create the table
        $table = ErdTable::create([
            'project_id' => $this->project->id,
            'name' => $this->tableForm['name'],
            'display_name' => $this->tableForm['display_name'],
            'description' => $this->tableForm['description'] ?? '',
            'position_x' => $positionX,
            'position_y' => $positionY,
            'width' => 280,
            'height' => 200,
            'color' => $this->tableForm['color'] ?? '#3b82f6',
        ]);

        // Create fields
        $this->createTableFields($table->id);

        // Reset form and close modal
        $this->resetTableForm();

        Notification::make()
            ->title('Table Created')
            ->body("Table '{$table->display_name}' has been created successfully with " . count($this->tableForm['fields'] ?? []) . " fields.")
            ->success()
            ->send();
    }

    private function updateExistingTable(): void
    {
        $table = ErdTable::find($this->editingTableId);
        if (!$table || $table->project_id !== $this->project->id) return;

        // Check for unique table name (excluding current table)
        $existingTable = collect($this->tables)
            ->where('id', '!=', $this->editingTableId)
            ->firstWhere('name', $this->tableForm['name']);

        if ($existingTable) {
            Notification::make()
                ->title('Validation Error')
                ->body('A table with this name already exists.')
                ->danger()
                ->send();
            return;
        }

        // Update table basic info
        $table->update([
            'name' => $this->tableForm['name'],
            'display_name' => $this->tableForm['display_name'],
            'description' => $this->tableForm['description'] ?? '',
            'color' => $this->tableForm['color'] ?? '#3b82f6',
        ]);

        // Update fields
        $this->updateTableFields($table->id);

        // Reset form and close modal
        $this->resetTableForm();

        Notification::make()
            ->title('Table Updated')
            ->body("Table '{$table->display_name}' has been updated successfully.")
            ->success()
            ->send();
    }

    private function createTableFields(int $tableId): void
    {
        if (empty($this->tableForm['fields'])) return;

        foreach ($this->tableForm['fields'] as $index => $fieldData) {
            if (empty($fieldData['name'])) continue;

            ErdField::create([
                'table_id' => $tableId,
                'name' => $fieldData['name'],
                'type' => $fieldData['type'] ?? 'varchar',
                'length' => $fieldData['length'] ?: null,
                'is_nullable' => $fieldData['is_nullable'] ?? true,
                'is_primary' => $fieldData['is_primary'] ?? false,
                'is_foreign_key' => $fieldData['is_foreign_key'] ?? false,
                'is_auto_increment' => $fieldData['is_auto_increment'] ?? false,
                'default_value' => $fieldData['default_value'] ?? null,
                'order' => $index,
                'form_type' => $this->getFormType($fieldData['type'] ?? 'varchar'),
            ]);
        }
    }

    private function updateTableFields(int $tableId): void
    {
        if (empty($this->tableForm['fields'])) return;

        // Get existing fields
        $existingFields = ErdField::where('table_id', $tableId)->get()->keyBy('id');
        $processedFieldIds = [];

        foreach ($this->tableForm['fields'] as $index => $fieldData) {
            if (empty($fieldData['name'])) continue;

            $fieldId = $fieldData['id'] ?? null;

            if ($fieldId && $existingFields->has($fieldId)) {
                // Update existing field
                $existingFields[$fieldId]->update([
                    'name' => $fieldData['name'],
                    'type' => $fieldData['type'] ?? 'varchar',
                    'length' => $fieldData['length'] ?: null,
                    'is_nullable' => $fieldData['is_nullable'] ?? true,
                    'is_primary' => $fieldData['is_primary'] ?? false,
                    'is_foreign_key' => $fieldData['is_foreign_key'] ?? false,
                    'is_auto_increment' => $fieldData['is_auto_increment'] ?? false,
                    'default_value' => $fieldData['default_value'] ?? null,
                    'order' => $index,
                    'form_type' => $this->getFormType($fieldData['type'] ?? 'varchar'),
                ]);
                $processedFieldIds[] = $fieldId;
            } else {
                // Create new field
                $newField = ErdField::create([
                    'table_id' => $tableId,
                    'name' => $fieldData['name'],
                    'type' => $fieldData['type'] ?? 'varchar',
                    'length' => $fieldData['length'] ?: null,
                    'is_nullable' => $fieldData['is_nullable'] ?? true,
                    'is_primary' => $fieldData['is_primary'] ?? false,
                    'is_foreign_key' => $fieldData['is_foreign_key'] ?? false,
                    'is_auto_increment' => $fieldData['is_auto_increment'] ?? false,
                    'default_value' => $fieldData['default_value'] ?? null,
                    'order' => $index,
                    'form_type' => $this->getFormType($fieldData['type'] ?? 'varchar'),
                ]);
                $processedFieldIds[] = $newField->id;
            }
        }

        // Delete fields that were removed
        $fieldsToDelete = $existingFields->keys()->diff($processedFieldIds);
        if ($fieldsToDelete->isNotEmpty()) {
            ErdField::whereIn('id', $fieldsToDelete)->delete();
        }
    }

    private function resetTableForm(): void
    {
        $this->tableForm = [];
        $this->editingTableId = null;
        $this->showTableModal = false;
        $this->loadProjectData();
    }

    public function testDataLoad(): void
    {
        if (!$this->project) {
            Notification::make()
                ->title('Debug: No Project')
                ->body('Project is null')
                ->warning()
                ->send();
            return;
        }

        $tableCount = $this->project->tables()->count();
        $relationshipCount = $this->project->relationships()->count();

        Notification::make()
            ->title('Debug: Data Loaded')
            ->body("Project ID: {$this->project->id}, Tables: {$tableCount}, Relations: {$relationshipCount}, Loaded Tables: " . count($this->tables))
            ->info()
            ->send();
    }

    public function createRelationship(array $data): void
    {
        if (!$this->project) return;

        ErdRelationship::create([
            'project_id' => $this->project->id,
            'source_table_id' => $data['source_table_id'],
            'target_table_id' => $data['target_table_id'],
            'source_field_id' => $data['source_field_id'] ?: null,
            'target_field_id' => $data['target_field_id'] ?: null,
            'type' => $data['type'],
            'foreign_key' => $data['foreign_key'] ?? null,
            'local_key' => $data['local_key'] ?? 'id',
            'pivot_table' => $data['pivot_table'] ?? null,
            'relationship_name' => $data['relationship_name'] ?? null,
            'cardinality_source' => $this->getCardinalityFromType($data['type'], 'source'),
            'cardinality_target' => $this->getCardinalityFromType($data['type'], 'target')
        ]);

        $this->loadProjectData();

        Notification::make()
            ->title('Relationship Created')
            ->body("New {$data['type']} relationship has been created successfully.")
            ->success()
            ->send();
    }

    private function getCardinalityFromType(string $type, string $side): string
    {
        $cardinalityMap = [
            'belongsTo' => ['source' => 'exactly_one', 'target' => 'zero_or_many'],
            'hasOne' => ['source' => 'exactly_one', 'target' => 'zero_or_one'],
            'hasMany' => ['source' => 'exactly_one', 'target' => 'zero_or_many'],
            'belongsToMany' => ['source' => 'zero_or_many', 'target' => 'zero_or_many'],
            'hasOneThrough' => ['source' => 'exactly_one', 'target' => 'zero_or_one'],
            'hasManyThrough' => ['source' => 'exactly_one', 'target' => 'zero_or_many'],
            'morphTo' => ['source' => 'exactly_one', 'target' => 'zero_or_many'],
            'morphOne' => ['source' => 'exactly_one', 'target' => 'zero_or_one'],
            'morphMany' => ['source' => 'exactly_one', 'target' => 'zero_or_many'],
            'morphToMany' => ['source' => 'zero_or_many', 'target' => 'zero_or_many']
        ];

        return $cardinalityMap[$type][$side] ?? 'zero_or_many';
    }

    public function updateRelationship(int $relationshipId, array $data): void
    {
        $relationship = ErdRelationship::find($relationshipId);
        if ($relationship && $relationship->project_id === $this->project->id) {
            $relationship->update([
                'source_table_id' => $data['source_table_id'],
                'target_table_id' => $data['target_table_id'],
                'source_field_id' => $data['source_field_id'] ?: null,
                'target_field_id' => $data['target_field_id'] ?: null,
                'type' => $data['type'],
                'cardinality_source' => $data['cardinality_source'] ?? 'exactly_one',
                'cardinality_target' => $data['cardinality_target'] ?? 'zero_or_many'
            ]);

            $this->loadProjectData();

            Notification::make()
                ->title('Relationship Updated')
                ->body('Relationship has been updated successfully.')
                ->success()
                ->send();
        }
    }

    public function deleteRelationship(int $relationshipId): void
    {
        $relationship = ErdRelationship::find($relationshipId);
        if ($relationship && $relationship->project_id === $this->project->id) {
            $relationship->delete();
            $this->loadProjectData();

            Notification::make()
                ->title('Relationship Deleted')
                ->body('Relationship has been deleted successfully.')
                ->success()
                ->send();
        }
    }



    public function exportSQL(): void
    {
        // Super admins can export, or users with specific permission
        if (!auth()->user()->hasRole('Super Admin') && !auth()->user()->can('export_sql_from_erd')) {
            Notification::make()
                ->title('Access Denied')
                ->body('You do not have permission to export SQL.')
                ->danger()
                ->send();
            return;
        }

        if (!$this->project) return;

        $exportService = new SqlExportService();
        $this->sqlExport = $exportService->exportProject($this->project);
        $this->showExportModal = true;
    }

    public function importSQL(): void
    {
        // Super admins can import, or users with specific permission
        if (!auth()->user()->hasRole('Super Admin') && !auth()->user()->can('import_sql_to_erd')) {
            Notification::make()
                ->title('Access Denied')
                ->body('You do not have permission to import SQL.')
                ->danger()
                ->send();
            return;
        }

        $this->showImportModal = true;
    }

    public function processImportSQL(): void
    {
        if (!$this->project || empty($this->sqlImport)) return;

        try {
            $importService = new SqlImportService();
            $importService->importToProject($this->project, $this->sqlImport);
            
            $this->loadProjectData();
            $this->showImportModal = false;
            $this->sqlImport = '';

            Notification::make()
                ->title('SQL Imported')
                ->body('SQL has been successfully imported into your ERD project.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Import Failed')
                ->body('Error importing SQL: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function exportToModuleBuilder(): void
    {
        // Super admins can integrate, or users with specific permission
        if (!auth()->user()->hasRole('Super Admin') && !auth()->user()->can('integrate_erd_module_builder')) {
            Notification::make()
                ->title('Access Denied')
                ->body('You do not have permission to integrate with Module Builder.')
                ->danger()
                ->send();
            return;
        }

        Notification::make()
            ->title('Integration Available')
            ->body('Use the "Load from ERD Designer" button in the Module Builder to import this project.')
            ->info()
            ->send();

        // Redirect to Module Builder
        $this->redirect('/admin/enhanced-module-builder');
    }

    public function deleteTable(int $tableId): void
    {
        $table = ErdTable::find($tableId);
        if ($table && $table->project_id === $this->project->id) {
            $table->delete();
            $this->loadProjectData();

            Notification::make()
                ->title('Table Deleted')
                ->body('Table has been successfully deleted.')
                ->success()
                ->send();
        }
    }

    public function updateTable(int $tableId, array $data): void
    {
        $table = ErdTable::find($tableId);
        if ($table && $table->project_id === $this->project->id) {
            $table->update($data);
            $this->loadProjectData();
        }
    }

    public function saveTablePosition(int $tableId, float $x, float $y): void
    {
        $table = ErdTable::find($tableId);
        if ($table && $table->project_id === $this->project->id) {
            $table->update([
                'position_x' => $x,
                'position_y' => $y
            ]);

            // Update the local tables array to keep frontend in sync
            foreach ($this->tables as &$localTable) {
                if ($localTable['id'] == $tableId) {
                    $localTable['position_x'] = $x;
                    $localTable['position_y'] = $y;
                    break;
                }
            }

            \Log::info("Table position saved: {$table->name} at ({$x}, {$y})");
        }
    }

    public function updateField(int $fieldId, array $data): void
    {
        $field = ErdField::find($fieldId);
        if ($field && $field->table->project_id === $this->project->id) {
            $field->update($data);
            $this->loadProjectData();
        }
    }

    public function deleteField(int $fieldId): void
    {
        $field = ErdField::find($fieldId);
        if ($field && $field->table->project_id === $this->project->id) {
            $field->delete();
            $this->loadProjectData();

            Notification::make()
                ->title('Field Deleted')
                ->body('Field has been successfully deleted.')
                ->success()
                ->send();
        }
    }

    public function addFieldToTable(int $tableId): void
    {
        $table = ErdTable::find($tableId);
        if ($table && $table->project_id === $this->project->id) {
            ErdField::create([
                'table_id' => $tableId,
                'name' => 'new_field',
                'type' => 'varchar',
                'length' => 255,
                'is_nullable' => true,
                'is_primary' => false,
                'is_foreign_key' => false
            ]);

            $this->loadProjectData();

            Notification::make()
                ->title('Field Added')
                ->body('New field has been added to the table.')
                ->success()
                ->send();
        }
    }

    public function createField(int $tableId, array $data): void
    {
        $table = ErdTable::find($tableId);
        if ($table && $table->project_id === $this->project->id) {
            ErdField::create(array_merge($data, [
                'table_id' => $tableId,
                'order' => $table->fields()->count(),
                'form_type' => $this->getFormType($data['type'])
            ]));

            $this->loadProjectData();

            Notification::make()
                ->title('Field Created')
                ->body('New field has been created successfully.')
                ->success()
                ->send();
        }
    }

    public function createDemoTables(): void
    {
        if (!$this->project) return;

        // Create Users table (top-left)
        $usersTable = ErdTable::create([
            'project_id' => $this->project->id,
            'name' => 'users',
            'display_name' => 'Users',
            'description' => 'System users and customers',
            'position_x' => 40,
            'position_y' => 40,
            'width' => 220,
            'height' => 180,
            'color' => '#3b82f6',
        ]);

        $this->createFieldsForTable($usersTable, [
            ['name' => 'id', 'type' => 'bigint', 'is_primary' => true, 'is_auto_increment' => true, 'is_nullable' => false],
            ['name' => 'name', 'type' => 'varchar', 'length' => 255, 'is_nullable' => false],
            ['name' => 'email', 'type' => 'varchar', 'length' => 255, 'is_nullable' => false],
            ['name' => 'created_at', 'type' => 'timestamp', 'is_nullable' => true],
            ['name' => 'updated_at', 'type' => 'timestamp', 'is_nullable' => true],
        ]);

        // Create Categories table (bottom-left)
        $categoriesTable = ErdTable::create([
            'project_id' => $this->project->id,
            'name' => 'categories',
            'display_name' => 'Categories',
            'description' => 'Product categories',
            'position_x' => 40,
            'position_y' => 280,
            'width' => 220,
            'height' => 200,
            'color' => '#10b981',
        ]);

        $this->createFieldsForTable($categoriesTable, [
            ['name' => 'id', 'type' => 'bigint', 'is_primary' => true, 'is_auto_increment' => true, 'is_nullable' => false],
            ['name' => 'name', 'type' => 'varchar', 'length' => 255, 'is_nullable' => false],
            ['name' => 'slug', 'type' => 'varchar', 'length' => 255, 'is_nullable' => false],
            ['name' => 'description', 'type' => 'text', 'is_nullable' => true],
            ['name' => 'image', 'type' => 'varchar', 'length' => 255, 'is_nullable' => true],
            ['name' => 'is_active', 'type' => 'tinyint', 'length' => 1, 'is_nullable' => false],
            ['name' => 'created_at', 'type' => 'timestamp', 'is_nullable' => true],
            ['name' => 'updated_at', 'type' => 'timestamp', 'is_nullable' => true],
        ]);

        // Create Products table (center)
        $productsTable = ErdTable::create([
            'project_id' => $this->project->id,
            'name' => 'products',
            'display_name' => 'Products',
            'description' => 'Product catalog',
            'position_x' => 320,
            'position_y' => 280,
            'width' => 220,
            'height' => 180,
            'color' => '#f59e0b',
        ]);

        $this->createFieldsForTable($productsTable, [
            ['name' => 'id', 'type' => 'bigint', 'is_primary' => true, 'is_auto_increment' => true, 'is_nullable' => false],
            ['name' => 'category_id', 'type' => 'bigint', 'is_foreign_key' => true, 'is_nullable' => false],
            ['name' => 'name', 'type' => 'varchar', 'length' => 255, 'is_nullable' => false],
            ['name' => 'created_at', 'type' => 'timestamp', 'is_nullable' => true],
            ['name' => 'updated_at', 'type' => 'timestamp', 'is_nullable' => true],
        ]);

        // Create Orders table (top-right)
        $ordersTable = ErdTable::create([
            'project_id' => $this->project->id,
            'name' => 'orders',
            'display_name' => 'Orders',
            'description' => 'Customer orders',
            'position_x' => 600,
            'position_y' => 40,
            'width' => 220,
            'height' => 200,
            'color' => '#8b5cf6',
        ]);

        $this->createFieldsForTable($ordersTable, [
            ['name' => 'id', 'type' => 'bigint', 'is_primary' => true, 'is_auto_increment' => true, 'is_nullable' => false],
            ['name' => 'user_id', 'type' => 'bigint', 'is_foreign_key' => true, 'is_nullable' => false],
            ['name' => 'order_number', 'type' => 'varchar', 'length' => 100, 'is_nullable' => false],
            ['name' => 'status', 'type' => 'enum', 'is_nullable' => false],
            ['name' => 'total_amount', 'type' => 'decimal', 'is_nullable' => false],
            ['name' => 'created_at', 'type' => 'timestamp', 'is_nullable' => true],
            ['name' => 'updated_at', 'type' => 'timestamp', 'is_nullable' => true],
        ]);

        // Create Order Items table (bottom-right)
        $orderItemsTable = ErdTable::create([
            'project_id' => $this->project->id,
            'name' => 'order_items',
            'display_name' => 'Order Items',
            'description' => 'Items in each order',
            'position_x' => 600,
            'position_y' => 320,
            'width' => 220,
            'height' => 200,
            'color' => '#ef4444',
        ]);

        $this->createFieldsForTable($orderItemsTable, [
            ['name' => 'id', 'type' => 'bigint', 'is_primary' => true, 'is_auto_increment' => true, 'is_nullable' => false],
            ['name' => 'order_id', 'type' => 'bigint', 'is_foreign_key' => true, 'is_nullable' => false],
            ['name' => 'product_id', 'type' => 'bigint', 'is_foreign_key' => true, 'is_nullable' => false],
            ['name' => 'quantity', 'type' => 'int', 'is_nullable' => false],
            ['name' => 'unit_price', 'type' => 'decimal', 'is_nullable' => false],
            ['name' => 'total_price', 'type' => 'decimal', 'is_nullable' => false],
            ['name' => 'created_at', 'type' => 'timestamp', 'is_nullable' => true],
            ['name' => 'updated_at', 'type' => 'timestamp', 'is_nullable' => true],
        ]);

        // Create relationships
        $this->createDemoRelationships();
    }

    private function createFieldsForTable(ErdTable $table, array $fields): void
    {
        foreach ($fields as $index => $fieldData) {
            ErdField::create(array_merge($fieldData, [
                'table_id' => $table->id,
                'order' => $index,
                'form_type' => $this->getFormType($fieldData['type']),
            ]));
        }
    }

    private function getFormType(string $type): string
    {
        $typeMap = [
            'varchar' => 'text',
            'text' => 'textarea',
            'bigint' => 'number',
            'int' => 'number',
            'timestamp' => 'datetime',
            'decimal' => 'number',
            'tinyint' => 'toggle',
            'enum' => 'select',
        ];

        return $typeMap[$type] ?? 'text';
    }

    public function downloadSQL()
    {
        if (!$this->project) {
            Notification::make()
                ->title('Error')
                ->body('No project selected for export.')
                ->danger()
                ->send();
            return;
        }

        $sql = $this->generateSQL();

        if (empty($sql)) {
            Notification::make()
                ->title('No Data')
                ->body('No tables found to export.')
                ->warning()
                ->send();
            return;
        }

        // Create a downloadable response
        $filename = 'erd_' . Str::slug($this->project->name) . '_' . date('Y-m-d_H-i-s') . '.sql';

        return response()->streamDownload(function () use ($sql) {
            echo $sql;
        }, $filename, [
            'Content-Type' => 'application/sql',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }

    private function generateSQL(): string
    {
        $sql = "-- ERD Export for Project: {$this->project->name}\n";
        $sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
        $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";

        // Generate CREATE TABLE statements
        foreach ($this->tables as $table) {
            $sql .= $this->generateTableSQL($table);
            $sql .= "\n";
        }

        // Generate foreign key constraints
        $sql .= "\n-- Foreign Key Constraints\n";
        foreach ($this->relationships as $relationship) {
            $sql .= $this->generateForeignKeySQL($relationship);
        }

        $sql .= "\nSET FOREIGN_KEY_CHECKS = 1;\n";

        return $sql;
    }

    private function generateTableSQL(array $table): string
    {
        $sql = "-- Table: {$table['name']}\n";
        $sql .= "DROP TABLE IF EXISTS `{$table['name']}`;\n";
        $sql .= "CREATE TABLE `{$table['name']}` (\n";

        $fields = [];
        $primaryKeys = [];

        foreach ($table['fields'] as $field) {
            $fieldSQL = "  `{$field['name']}` ";

            // Add field type
            $fieldSQL .= strtoupper($field['type']);

            // Add length if specified
            if (!empty($field['length']) && in_array($field['type'], ['varchar', 'char', 'decimal'])) {
                $fieldSQL .= "({$field['length']})";
            }

            // Add nullable
            $fieldSQL .= $field['is_nullable'] ? ' NULL' : ' NOT NULL';

            // Add auto increment
            if ($field['is_auto_increment']) {
                $fieldSQL .= ' AUTO_INCREMENT';
            }

            // Add default value
            if (!empty($field['default_value'])) {
                $fieldSQL .= " DEFAULT '{$field['default_value']}'";
            }

            $fields[] = $fieldSQL;

            // Track primary keys
            if ($field['is_primary']) {
                $primaryKeys[] = "`{$field['name']}`";
            }
        }

        // Add primary key constraint
        if (!empty($primaryKeys)) {
            $fields[] = "  PRIMARY KEY (" . implode(', ', $primaryKeys) . ")";
        }

        $sql .= implode(",\n", $fields);
        $sql .= "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n\n";

        return $sql;
    }

    private function generateForeignKeySQL(array $relationship): string
    {
        if (empty($relationship['source_field_id']) || empty($relationship['target_field_id'])) {
            return "";
        }

        $sourceTable = collect($this->tables)->firstWhere('id', $relationship['source_table_id']);
        $targetTable = collect($this->tables)->firstWhere('id', $relationship['target_table_id']);

        if (!$sourceTable || !$targetTable) {
            return "";
        }

        $sourceField = collect($sourceTable['fields'])->firstWhere('id', $relationship['source_field_id']);
        $targetField = collect($targetTable['fields'])->firstWhere('id', $relationship['target_field_id']);

        if (!$sourceField || !$targetField) {
            return "";
        }

        $constraintName = "fk_{$sourceTable['name']}_{$sourceField['name']}";

        return "ALTER TABLE `{$sourceTable['name']}` ADD CONSTRAINT `{$constraintName}` " .
               "FOREIGN KEY (`{$sourceField['name']}`) REFERENCES `{$targetTable['name']}` (`{$targetField['name']}`) " .
               "ON DELETE CASCADE ON UPDATE CASCADE;\n";
    }

    public function getTitle(): string
    {
        return $this->project ?
            "ERD Designer - {$this->project->name}" :
            'ERD Designer';
    }
}
